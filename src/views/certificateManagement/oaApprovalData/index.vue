<template>
  <!-- <el-tabs v-model="activeName" @tab-click="handleClick">
    <el-tab-pane label="oa审批流程" name="first">
      <businessPayment></businessPayment>
    </el-tab-pane>

  </el-tabs> -->
  <businessPayment></businessPayment>
</template>
<script>
import businessPayment from './businessPayment.vue'
export default{
  name: 'OaApprovalData',
  components: { businessPayment },
  data (){
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
}
</script>
<style scoped>
</style>
