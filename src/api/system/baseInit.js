import request from "../../utils/request";
const BASE_REQUEST = '/api/baseInit'

export function getDictionaryList(keys,deptCode=undefined,wxDepartmentId=undefined) {
  return request({
    url: `${BASE_REQUEST}/getDictionaryList`,
    method: 'get',
    params:{keys:keys,deptCode:deptCode,wxDepartmentId:wxDepartmentId}
  })
}

export function getAllSysSupplier(deptCode=undefined) {
  return request({
    url: `${BASE_REQUEST}/getAllSysSupplier`,
    method: 'get',
    params:{deptCode:deptCode}
  })
}


export function getAllCusTomer(deptCode=undefined) {
  return request({
    url: `${BASE_REQUEST}/getAllCusTomer`,
    method: 'get',
    params:{deptCode:deptCode}
  })
}

export function getAllUser(deptCode=undefined) {
  return request({
    url: `${BASE_REQUEST}/getAllUser`,
    method: 'get',
    params:{deptCode:deptCode}
  })
}

export function getSysSupplierContactList(id,deptCode=undefined,wxDepartmentId=undefined) {
  return request({
    url: `${BASE_REQUEST}/getSysSupplierContactList`,
    method: 'get',
    params:{supplierId:id,deptCode:deptCode,wxDepartmentId:wxDepartmentId}
  })
}

export function getPersonalListById(id) {
  return request({
    url: `${BASE_REQUEST}/getPersonalListById`,
    method: 'get',
    params:{id:id}
  })
}

export function getUserInfoById(id,deptCode=undefined,wxDepartmentId=undefined) {
  return request({
    url: `${BASE_REQUEST}/getUserInfoById`,
    method: 'get',
    params:{userid:id,deptCode:deptCode,wxDepartmentId:wxDepartmentId}
  })
}
export function getContractStatusByShipLineId(id,deptCode=undefined,wxDepartmentId=undefined) {
  return request({
    url: `${BASE_REQUEST}/getContractStatusByShipLineId`,
    method: 'get',
    params:{id:id,deptCode:deptCode,wxDepartmentId:wxDepartmentId}
  })
}
export function selectbycode(code) {
  return request({
    url:`${BASE_REQUEST}/selectbycode`,
    method: 'get',
    params:{code:code}
  })
}

export function selectbyId(id) {
  return request({
    url: `${BASE_REQUEST}/selectbyId`,
    method: 'get',
    params: {id: id}
  })
}

export function getPayment(paymentId,type,deptCode=undefined,wxDepartmentId=undefined) {
  return request({
    url:`${BASE_REQUEST}/getPayment`,
    method: 'get',
    params: {paymentId: paymentId,type:type,deptCode,wxDepartmentId}
  })
}

export function fanDianCustomerInfo({ costAggregationIds, deptCode = undefined, wxDepartmentId = undefined }) {
  return request({
    url:`${BASE_REQUEST}/fanDianCustomerInfo`,
    method: 'get',
    params: {costAggregationIds: costAggregationIds,deptCode,wxDepartmentId}
  })
}

export function heTongInfo({ costAggregationIds,type, deptCode = undefined, wxDepartmentId = undefined }) {
  return request({
    url:`${BASE_REQUEST}/heTongInfo`,
    method: 'post',
    data: {costAggregationIds: costAggregationIds,type,deptCode,wxDepartmentId}
  })
}

export function getCommonAndRecordByKey(key,deptCode=undefined) {
  return request({
    url:`${BASE_REQUEST}/getCommonAndRecordByKey`,
    params: {
      key: key,
      deptCode:deptCode
    }
  })
}

export function saveRecord(key,datakey) {
  return request({
    url:`${BASE_REQUEST}/saveRecord`,
    params: {
      key:key,
      datakey:datakey
    }
  })
}


export function getShipLineDingjingById(shipLineId, processId,wxDepartmentId) {
  return request({
    url:`${BASE_REQUEST}/getShipLineDingjingById`,
    params: {shipLineId: shipLineId, processId: processId ,wxDepartmentId:wxDepartmentId}
  })
}

export function saveSysCustomerSecondlevel(name,deptCode=undefined) {
  return request({
    url:`${BASE_REQUEST}/saveSysCustomerSecondlevel`,
    params: {
      name: name,
      deptCode
    }
  })
}
export function saveSysSupplierName(name,deptCode=undefined) {
  return request({
    url:`${BASE_REQUEST}/saveSysSupplierName`,
    params: {
      name: name,
      deptCode
    }
  })
}

export function shipLinePageByStatus({shipChuanQiTime,shipName,status,pageNum,pageSize}) {
  return request({
    url:`${BASE_REQUEST}/shipLinePageByStatus`,
    method: 'get',
    params:{shipChuanQiTime,shipName,status,pageNum,pageSize}
  })
}

export function getYewuCostomerCostNoComfirm({shipLineId}) {
  return request({
    url:`${BASE_REQUEST}/getYewuCostomerCostNoComfirm`,
    method: 'get',
    params:{shipLineId}
  })
}

export function getSupplierDictionaryList({Key,Code}) {
  return request({
    url:`${BASE_REQUEST}/getSupplierDictionaryList`,
    method: 'get',
    params:{Key,Code}
  })
}
export function getShipCostNoComfirm({shipLineId}) {
  return request({
    url:`${BASE_REQUEST}/getShipCostNoComfirm`,
    method: 'get',
    params:{shipLineId}
  })
}

export function getGoodsCostNoComfirmByShipLineIds({goodsCostIds}) {
  return request({
    url:`${BASE_REQUEST}/getGoodsCostNoComfirmByShipLineIds`,
    method: 'post',
    data:{goodsCostIds}
  })
}

export function getShipCostNoComfirmByShipLineIds({shipLineIds}) {
  return request({
    url:`${BASE_REQUEST}/getShipCostNoComfirmByShipLineIds`,
    method: 'post',
    data:{shipLineIds}
  })
}
export function getAllShipPort(deptCode=undefined) {
  return request({
    url:`${BASE_REQUEST}/getAllShipPort`,
    method: 'get',
    params:{deptCode:deptCode}
  })
}
export function getAllSysWharf(deptCode=undefined) {
  return request({
    url:`${BASE_REQUEST}/getAllSysWharf`,
    method: 'get',
    params:{deptCode:deptCode}
  })
}


// 凭证 list save update
// {list:[{}]}
//   {
        //     "children": [
        //         {
        //             "accountingSubjects": "1002",
        //             "borrowing": 1,
        //             "cerateTime": "2024-11-11 14:04:06",
        //             "certTemplId": 47,
        //             "fieldCode": "actualFreight",
        //             "fieldSummary": "应付账款",
        //             "findSub": "",
        //             "id": 152,
        //             "lastTime": "2025-05-18 17:09:55",
        //             "name": "油品消耗",
        //             "ordernum": 2,
        //             "templAbstract": "应付账款"
        //         },
        //         {
        //             "accountingSubjects": "1002",
        //             "borrowing": -1,
        //             "cerateTime": "2024-11-11 14:04:06",
        //             "certTemplId": 47,
        //             "fieldCode": "actualFreight",
        //             "fieldSummary": "主营业务成本",
        //             "findSub": "",
        //             "id": 153,
        //             "lastTime": "2025-05-18 17:09:55",
        //             "name": "油品消耗",
        //             "ordernum": 2,
        //             "templAbstract": "主营业务成本"
        //         }
        //     ],
        //     "parent": {
        //         "appCode": "prod1",
        //         "code": "oilWaterConsumption_pzd",
        //         "createTime": "2024-12-17 15:32:32",
        //         "id": 47,
        //         "lastTime": "2024-12-18 15:15:19",
        //         "name": "油品消耗",
        //         "regularExpressions": ""
        //     }
        // }
export function getCertTemplList(name=''){
   return request({
    url:`${BASE_REQUEST}/getCertTemplList`,
    method: 'get',
    params:{name}
  })
}
// certTemplVo { parent:{},children:[]}
export function saveCertTemplVo(certTemplVo){
   return request({
    url:`${BASE_REQUEST}/saveCertTemplVo`,
    method: 'post',
    data:certTemplVo
  })
}

export function updateCertTemplVo(certTemplVo){
   return request({
    url:`${BASE_REQUEST}/updateCertTemplVo`,
    method: 'post',
    data:certTemplVo
  })
}

