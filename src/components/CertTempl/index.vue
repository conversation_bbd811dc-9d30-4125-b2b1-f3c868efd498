<template>
  <section class="cert-templ-container">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入模板名称"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleSearch"
          />
        </el-form-item>
        <el-form-item label="编码">
          <el-input
            v-model="searchForm.code"
            placeholder="请输入模板编码"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
          <el-button type="success" icon="el-icon-plus" @click="handleAdd">新增</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="filteredCertTemplList"
        style="width: 100%"
        row-key="id"
        default-expand-all
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        border
        stripe
        size="small"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column label="模板名称" min-width="180" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="template-name">{{ getDisplayName(scope.row) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="模板编码" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag size="mini" type="info">{{ getDisplayCode(scope.row) }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="应用编码" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag size="mini" type="primary">{{ getDisplayAppCode(scope.row) }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="字段编码" min-width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.fieldCode" class="field-code">{{ scope.row.fieldCode }}</span>
            <span v-else>--</span>
          </template>
        </el-table-column>

        <el-table-column label="摘要" min-width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="condition-text">{{ getDisplaySummary(scope.row) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="会计科目" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag v-if="scope.row.accountingSubjects" size="mini" type="success">
              {{ scope.row.accountingSubjects }}
            </el-tag>
            <span v-else>--</span>
          </template>
        </el-table-column>

        <el-table-column label="借贷方向" width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag v-if="scope.row.borrowing === 1" size="mini" type="danger">借</el-tag>
            <el-tag v-else-if="scope.row.borrowing === -1" size="mini" type="success">贷</el-tag>
            <span v-else>--</span>
          </template>
        </el-table-column>

        <el-table-column label="正则表达式" min-width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            <code v-if="getDisplayRegex(scope.row)" class="regex-code">
              {{ getDisplayRegex(scope.row) }}
            </code>
            <span v-else>--</span>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="160" show-overflow-tooltip>
          <template slot-scope="scope">
            <i class="el-icon-time"></i>
            <span>{{ formatDateTime(getDisplayCreateTime(scope.row)) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="最后更新" width="160" show-overflow-tooltip>
          <template slot-scope="scope">
            <i class="el-icon-edit"></i>
            <span>{{ formatDateTime(getDisplayLastTime(scope.row)) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看</el-button>
            <el-button
              size="mini"
              type="warning"
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 模板数据详情对话框 -->
    <el-dialog
      :visible.sync="detailDialogVisible"
      title="模板数据详情"
      width="80%"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentTemplate" class="template-detail">
        <!-- 父级模板信息 -->
        <div v-if="currentTemplate.parent" class="parent-section">
          <h3>模板基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="模板名称">
              {{ currentTemplate.parent.name || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="模板编码">
              {{ currentTemplate.parent.code || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="应用编码">
              {{ currentTemplate.parent.appCode || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="模板ID">
              {{ currentTemplate.parent.id || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="正则表达式" :span="2">
              <code v-if="currentTemplate.parent.regularExpressions" class="regex-code">
                {{ currentTemplate.parent.regularExpressions }}
              </code>
              <span v-else>--</span>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(currentTemplate.parent.createTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="最后更新">
              {{ formatDateTime(currentTemplate.parent.lastTime) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 子项详细信息 -->
        <div v-if="currentTemplate.children && currentTemplate.children.length" class="children-section">
          <h3>凭证明细项 ({{ currentTemplate.children.length }})</h3>
          <el-table :data="currentTemplate.children" border size="small" stripe>
            <el-table-column prop="name" label="名称" min-width="120" show-overflow-tooltip />
            <el-table-column prop="templAbstract" label="摘要" min-width="150" show-overflow-tooltip />
            <el-table-column prop="fieldCode" label="字段编码" min-width="120" show-overflow-tooltip />
            <el-table-column prop="fieldSummary" label="字段摘要" min-width="150" show-overflow-tooltip />
            <el-table-column prop="accountingSubjects" label="会计科目" width="100" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-tag v-if="scope.row.accountingSubjects" size="mini" type="success">
                  {{ scope.row.accountingSubjects }}
                </el-tag>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column prop="borrowing" label="借贷方向" width="80" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-tag v-if="scope.row.borrowing === 1" size="mini" type="danger">借</el-tag>
                <el-tag v-else-if="scope.row.borrowing === -1" size="mini" type="success">贷</el-tag>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column prop="ordernum" label="排序" width="60" />
            <el-table-column prop="id" label="ID" width="60" />
            <el-table-column label="创建时间" width="140" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ formatDateTime(scope.row.cerateTime) }}
              </template>
            </el-table-column>
            <el-table-column label="更新时间" width="140" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ formatDateTime(scope.row.lastTime) }}
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 单个子项信息（当查看的是子项时） -->
        <div v-else-if="!currentTemplate.parent && !currentTemplate.children" class="child-detail-section">
          <h3>凭证明细项信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="名称">
              {{ currentTemplate.name || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="摘要">
              {{ currentTemplate.templAbstract || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="字段编码">
              {{ currentTemplate.fieldCode || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="字段摘要">
              {{ currentTemplate.fieldSummary || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="会计科目">
              <el-tag v-if="currentTemplate.accountingSubjects" size="small" type="success">
                {{ currentTemplate.accountingSubjects }}
              </el-tag>
              <span v-else>--</span>
            </el-descriptions-item>
            <el-descriptions-item label="借贷方向">
              <el-tag v-if="currentTemplate.borrowing === 1" size="small" type="danger">借方</el-tag>
              <el-tag v-else-if="currentTemplate.borrowing === -1" size="small" type="success">贷方</el-tag>
              <span v-else>--</span>
            </el-descriptions-item>
            <el-descriptions-item label="排序号">
              {{ currentTemplate.ordernum || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="查找子项">
              {{ currentTemplate.findSub || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(currentTemplate.cerateTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="最后更新">
              {{ formatDateTime(currentTemplate.lastTime) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit(currentTemplate)">编辑</el-button>
      </div>
    </el-dialog>
  </section>
</template>
<script>
import { getCertTemplList } from '@/api/system/baseInit'
import dayjs from 'dayjs'

export default {
  name: 'CertTempl',
  data() {
    return {
      loading: false,
      certTemplList: [],
      searchForm: {
        name: '',
        code: ''
      },
      detailDialogVisible: false,
      currentTemplate: null
    }
  },
  computed: {
    // 处理新的数据结构 { parent: {}, children: [] }
    certTemplListTree() {
      if (!this.certTemplList || this.certTemplList.length === 0) {
        return []
      }

      // 新数据结构直接返回，每个项目已经包含 parent 和 children
      return this.certTemplList.map(item => {
        // 为树形表格添加必要的属性
        const treeItem = {
          ...item,
          // 使用 parent.id 作为行的唯一标识
          id: item.parent?.id || Math.random().toString(36).substr(2, 9),
          // 如果有 children，设置 hasChildren 为 true
          hasChildren: item.children && item.children.length > 0,
          // 将 children 数组中的每个子项也添加必要的属性
          children: item.children ? item.children.map(child => ({
            ...child,
            // 子项使用自己的 id
            id: child.id,
            hasChildren: false,
            // 标记这是一个子项
            isChild: true
          })) : []
        }
        return treeItem
      })
    },

    // 过滤后的列表（用于搜索）
    filteredCertTemplList() {
      if (!this.searchForm.name && !this.searchForm.code) {
        return this.certTemplListTree
      }

      const filterTree = (items) => {
        return items.filter(item => {
          // 搜索父级信息
          const parentName = item.parent?.name || ''
          const parentCode = item.parent?.code || ''

          // 搜索子项信息
          const childrenNames = item.children ? item.children.map(child => child.name || '').join(' ') : ''
          const childrenAbstracts = item.children ? item.children.map(child => child.templAbstract || '').join(' ') : ''

          const nameMatch = !this.searchForm.name ||
            parentName.toLowerCase().includes(this.searchForm.name.toLowerCase()) ||
            childrenNames.toLowerCase().includes(this.searchForm.name.toLowerCase()) ||
            childrenAbstracts.toLowerCase().includes(this.searchForm.name.toLowerCase())

          const codeMatch = !this.searchForm.code ||
            parentCode.toLowerCase().includes(this.searchForm.code.toLowerCase())

          return nameMatch && codeMatch
        })
      }

      return filterTree(this.certTemplListTree)
    }
  },
  created() {
    this.loadCertTemplList()
  },
  methods: {
    // 加载凭证模板列表
    async loadCertTemplList() {
      try {
        this.loading = true
        const res = await getCertTemplList(this.searchForm.name)
        this.certTemplList = res || []
      } catch (error) {
        console.error('加载凭证模板列表失败:', error)
        this.$message.error('加载凭证模板列表失败')
        this.certTemplList = []
      } finally {
        this.loading = false
      }
    },

    // 搜索处理
    handleSearch() {
      // 重新加载数据以应用搜索条件
      this.loadCertTemplList()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        name: '',
        code: ''
      }
      this.$message.success('搜索条件已重置')
    },

    // 新增模板
    handleAdd() {
      this.$message.info('新增功能开发中...')
      // TODO: 实现新增功能
    },

    // 查看详情
    handleView(row) {
      this.currentTemplate = { ...row }
      this.detailDialogVisible = true
    },

    // 编辑模板
    handleEdit(row) {
      this.$message.info('编辑功能开发中...')
      // TODO: 实现编辑功能
      console.log('编辑模板:', row)
    },

    // 删除模板
    handleDelete(row) {
      this.$confirm('确定要删除这个凭证模板吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.info('删除功能开发中...')
        // TODO: 实现删除功能
        console.log('删除模板:', row)
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 关闭详情对话框
    handleCloseDetail() {
      this.detailDialogVisible = false
      this.currentTemplate = null
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '--'
      return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
    },

    // 获取显示名称的辅助方法
    getDisplayName(row) {
      if (row.isChild) {
        // 子项显示自己的名称
        return row.name || '--'
      } else {
        // 父项显示 parent.name
        return row.parent?.name || '--'
      }
    },

    // 获取显示编码的辅助方法
    getDisplayCode(row) {
      if (row.isChild) {
        // 子项没有编码，显示 ID
        return row.id || '--'
      } else {
        // 父项显示 parent.code
        return row.parent?.code || '--'
      }
    },

    // 获取显示应用编码的辅助方法
    getDisplayAppCode(row) {
      if (row.isChild) {
        // 子项显示字段编码
        return row.fieldCode || '--'
      } else {
        // 父项显示 parent.appCode
        return row.parent?.appCode || '--'
      }
    },

    // 获取显示摘要的辅助方法
    getDisplaySummary(row) {
      if (row.isChild) {
        // 子项显示模板摘要或字段摘要
        return row.templAbstract || row.fieldSummary || '--'
      } else {
        // 父项显示子项数量
        const childCount = row.children ? row.children.length : 0
        return `包含 ${childCount} 个明细项`
      }
    },

    // 获取显示正则表达式的辅助方法
    getDisplayRegex(row) {
      if (row.isChild) {
        // 子项没有正则表达式
        return ''
      } else {
        // 父项显示 parent.regularExpressions
        return row.parent?.regularExpressions || ''
      }
    },

    // 获取显示创建时间的辅助方法
    getDisplayCreateTime(row) {
      if (row.isChild) {
        // 子项显示 cerateTime
        return row.cerateTime
      } else {
        // 父项显示 parent.createTime
        return row.parent?.createTime
      }
    },

    // 获取显示更新时间的辅助方法
    getDisplayLastTime(row) {
      if (row.isChild) {
        // 子项显示 lastTime
        return row.lastTime
      } else {
        // 父项显示 parent.lastTime
        return row.parent?.lastTime
      }
    }
  }
}
</script>
<style scoped>
.cert-templ-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.search-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.template-name {
  font-weight: 500;
  color: #303133;
}

.condition-text {
  color: #606266;
  font-size: 13px;
}

.regex-code {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 2px 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #e6a23c;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-detail {
  padding: 20px 0;
}

.template-data-section {
  margin-top: 20px;
}

.template-data-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.template-data-textarea {
  margin-top: 10px;
}

.template-data-textarea .el-textarea__inner {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  background-color: #f8f9fa;
}

.children-section {
  margin-top: 20px;
}

.children-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

/* 表格样式优化 */
.el-table {
  border-radius: 0;
}

.el-table .el-table__header-wrapper {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.el-table .el-table__body-wrapper {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

/* 按钮组样式 */
.el-button + .el-button {
  margin-left: 8px;
}

.el-button--mini {
  padding: 5px 8px;
  font-size: 12px;
}

/* 标签样式 */
.el-tag {
  border-radius: 4px;
}

.el-tag--mini {
  height: 20px;
  line-height: 18px;
  font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cert-templ-container {
    padding: 10px;
  }

  .search-container {
    padding: 15px;
  }

  .search-form .el-form-item {
    margin-bottom: 15px;
  }

  .el-button--mini {
    padding: 3px 6px;
    font-size: 11px;
  }
}

/* 加载状态样式 */
.el-loading-mask {
  border-radius: 8px;
}

/* 对话框样式优化 */
.el-dialog__header {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #e4e7ed;
}

.el-dialog__body {
  padding: 20px;
}

.el-dialog__footer {
  padding: 10px 20px 20px;
  border-top: 1px solid #e4e7ed;
}

/* 描述列表样式 */
.el-descriptions {
  margin-bottom: 20px;
}

.el-descriptions__label {
  font-weight: 500;
}

/* 空状态样式 */
.el-table__empty-block {
  padding: 40px 0;
}

.el-table__empty-text {
  color: #909399;
  font-size: 14px;
}
</style>
