<template>
  <section class="cert-templ-container">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入模板名称"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleSearch"
          />
        </el-form-item>
        <el-form-item label="编码">
          <el-input
            v-model="searchForm.code"
            placeholder="请输入模板编码"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
          <el-button type="success" icon="el-icon-plus" @click="handleAdd">新增</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="filteredCertTemplList"
        style="width: 100%"
        row-key="id"
        default-expand-all
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        border
        stripe
        size="small"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="parentName" label="模板名称" min-width="180" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="template-name">{{ scope.row.parentName || '--' }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="parentCode" label="模板编码" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag size="mini" type="info">{{ scope.row.parentCode || '--' }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="parentFlowCode" label="流程编码" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag size="mini" type="primary">{{ scope.row.parentFlowCode || '--' }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="parentConditionStr" label="条件" min-width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="condition-text">{{ scope.row.parentConditionStr || '--' }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="parentRegularExpressions" label="正则表达式" min-width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            <code v-if="scope.row.parentRegularExpressions" class="regex-code">
              {{ scope.row.parentRegularExpressions }}
            </code>
            <span v-else>--</span>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="160" show-overflow-tooltip>
          <template slot-scope="scope">
            <i class="el-icon-time"></i>
            <span>{{ formatDateTime(scope.row.parentCreateTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="最后更新" width="160" show-overflow-tooltip>
          <template slot-scope="scope">
            <i class="el-icon-edit"></i>
            <span>{{ formatDateTime(scope.row.parentLastTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看</el-button>
            <el-button
              size="mini"
              type="warning"
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 模板数据详情对话框 -->
    <el-dialog
      :visible.sync="detailDialogVisible"
      title="模板数据详情"
      width="80%"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentTemplate" class="template-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="模板名称">
            {{ currentTemplate.parentName || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="模板编码">
            {{ currentTemplate.parentCode || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="应用编码">
            {{ currentTemplate.parentAppCode || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="流程编码">
            {{ currentTemplate.parentFlowCode || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="条件字符串" :span="2">
            {{ currentTemplate.parentConditionStr || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="正则表达式" :span="2">
            <code v-if="currentTemplate.parentRegularExpressions" class="regex-code">
              {{ currentTemplate.parentRegularExpressions }}
            </code>
            <span v-else>--</span>
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="currentTemplate.parentTemplData" class="template-data-section">
          <h4>模板数据</h4>
          <el-input
            v-model="currentTemplate.parentTemplData"
            type="textarea"
            :rows="10"
            readonly
            class="template-data-textarea"
          />
        </div>

        <div v-if="currentTemplate.children && currentTemplate.children.length" class="children-section">
          <h4>子项列表 ({{ currentTemplate.children.length }})</h4>
          <el-table :data="currentTemplate.children" border size="small">
            <el-table-column prop="name" label="名称" show-overflow-tooltip />
            <el-table-column prop="templAbstract" label="摘要" show-overflow-tooltip />
            <el-table-column prop="id" label="ID" width="80" />
          </el-table>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit(currentTemplate)">编辑</el-button>
      </div>
    </el-dialog>
  </section>
</template>
<script>
import { getCertTemplList } from '@/api/system/baseInit'
import dayjs from 'dayjs'

export default {
  name: 'CertTempl',
  data() {
    return {
      loading: false,
      certTemplList: [],
      searchForm: {
        name: '',
        code: ''
      },
      detailDialogVisible: false,
      currentTemplate: null
    }
  },
  computed: {
    // 修复树形结构构建逻辑
    certTemplListTree() {
      if (!this.certTemplList || this.certTemplList.length === 0) {
        return []
      }

      // 创建一个映射来存储所有项目
      const itemMap = new Map()
      const rootItems = []

      // 首先将所有项目添加到映射中，并初始化children数组
      this.certTemplList.forEach(item => {
        const processedItem = {
          ...item,
          children: []
        }
        itemMap.set(item.parentId || item.id, processedItem)
      })

      // 构建树形结构
      this.certTemplList.forEach(item => {
        const processedItem = itemMap.get(item.parentId || item.id)

        if (item.parentId && item.parentId !== item.id) {
          // 这是一个子项
          const parent = itemMap.get(item.parentId)
          if (parent && parent !== processedItem) {
            parent.children.push(processedItem)
          } else {
            // 如果找不到父项，作为根项处理
            rootItems.push(processedItem)
          }
        } else {
          // 这是一个根项
          rootItems.push(processedItem)
        }
      })

      return rootItems
    },

    // 过滤后的列表（用于搜索）
    filteredCertTemplList() {
      if (!this.searchForm.name && !this.searchForm.code) {
        return this.certTemplListTree
      }

      const filterTree = (items) => {
        return items.filter(item => {
          const nameMatch = !this.searchForm.name ||
            (item.parentName && item.parentName.toLowerCase().includes(this.searchForm.name.toLowerCase()))
          const codeMatch = !this.searchForm.code ||
            (item.parentCode && item.parentCode.toLowerCase().includes(this.searchForm.code.toLowerCase()))

          // 检查子项是否匹配
          const childrenMatch = item.children && item.children.length > 0 ?
            filterTree(item.children).length > 0 : false

          if (nameMatch && codeMatch) {
            // 如果当前项匹配，保留所有子项
            return true
          } else if (childrenMatch) {
            // 如果子项匹配，保留当前项但过滤子项
            item.children = filterTree(item.children)
            return true
          }

          return false
        })
      }

      return filterTree(this.certTemplListTree)
    }
  },
  created() {
    this.loadCertTemplList()
  },
  methods: {
    // 加载凭证模板列表
    async loadCertTemplList() {
      try {
        this.loading = true
        const res = await getCertTemplList(this.searchForm.name)
        this.certTemplList = res.list || []
      } catch (error) {
        console.error('加载凭证模板列表失败:', error)
        this.$message.error('加载凭证模板列表失败')
        this.certTemplList = []
      } finally {
        this.loading = false
      }
    },

    // 搜索处理
    handleSearch() {
      // 由于使用了计算属性，搜索会自动触发
      this.$message.success('搜索完成')
      this.loadCertTemplList()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        name: '',
        code: ''
      }
      this.$message.success('搜索条件已重置')
    },

    // 新增模板
    handleAdd() {
      this.$message.info('新增功能开发中...')
      // TODO: 实现新增功能
    },

    // 查看详情
    handleView(row) {
      this.currentTemplate = { ...row }
      this.detailDialogVisible = true
    },

    // 编辑模板
    handleEdit(row) {
      this.$message.info('编辑功能开发中...')
      // TODO: 实现编辑功能
      console.log('编辑模板:', row)
    },

    // 删除模板
    handleDelete(row) {
      this.$confirm('确定要删除这个凭证模板吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.info('删除功能开发中...')
        // TODO: 实现删除功能
        console.log('删除模板:', row)
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 关闭详情对话框
    handleCloseDetail() {
      this.detailDialogVisible = false
      this.currentTemplate = null
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '--'
      return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
    }
  }
}
</script>
<style scoped>
.cert-templ-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.search-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.template-name {
  font-weight: 500;
  color: #303133;
}

.condition-text {
  color: #606266;
  font-size: 13px;
}

.regex-code {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 2px 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #e6a23c;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-detail {
  padding: 20px 0;
}

.template-data-section {
  margin-top: 20px;
}

.template-data-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.template-data-textarea {
  margin-top: 10px;
}

.template-data-textarea .el-textarea__inner {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  background-color: #f8f9fa;
}

.children-section {
  margin-top: 20px;
}

.children-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

/* 表格样式优化 */
.el-table {
  border-radius: 0;
}

.el-table .el-table__header-wrapper {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.el-table .el-table__body-wrapper {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

/* 按钮组样式 */
.el-button + .el-button {
  margin-left: 8px;
}

.el-button--mini {
  padding: 5px 8px;
  font-size: 12px;
}

/* 标签样式 */
.el-tag {
  border-radius: 4px;
}

.el-tag--mini {
  height: 20px;
  line-height: 18px;
  font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cert-templ-container {
    padding: 10px;
  }

  .search-container {
    padding: 15px;
  }

  .search-form .el-form-item {
    margin-bottom: 15px;
  }

  .el-button--mini {
    padding: 3px 6px;
    font-size: 11px;
  }
}

/* 加载状态样式 */
.el-loading-mask {
  border-radius: 8px;
}

/* 对话框样式优化 */
.el-dialog__header {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #e4e7ed;
}

.el-dialog__body {
  padding: 20px;
}

.el-dialog__footer {
  padding: 10px 20px 20px;
  border-top: 1px solid #e4e7ed;
}

/* 描述列表样式 */
.el-descriptions {
  margin-bottom: 20px;
}

.el-descriptions__label {
  font-weight: 500;
}

/* 空状态样式 */
.el-table__empty-block {
  padding: 40px 0;
}

.el-table__empty-text {
  color: #909399;
  font-size: 14px;
}
</style>
