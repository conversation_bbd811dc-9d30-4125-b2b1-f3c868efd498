<template>
<section>
  <el-table :data="certTemplListTree" style="width: 100%">
    <el-table-column prop="parentName" label="名称" width="180">
    </el-table-column>
    <el-table-column prop="parentCode" label="编码" width="180">
    </el-table-column>
    <el-table-column prop="parentFlowCode" label="流程编码">
    </el-table-column>
    <el-table-column prop="parentConditionStr" label="条件">
    </el-table-column>
    <el-table-column prop="parentRegularExpressions" label="正则">
    </el-table-column>
    <el-table-column prop="parentTemplData" label="模板数据">
    </el-table-column>
  </el-table>

</section>
</template>
<script>
import { getCertTemplList } from '@/api/system/baseInit'
export default {
  data() {
    return {
      certTemplList: []
    }
  },
  computed: {
    certTemplListTree() {
      // parent
     const parent=[]
     const pmap = new Map();
     for(let i=0;i<this.certTemplList.length;i++){
        const item = this.certTemplList[i]
        if(pmap.has(item.parentId)){
          pmap.get(item.parentId).children.push(item)
          continue
        }
        pmap[item.parentId] = item
        item.children = []
        item.children.push(item)
        parent.push(item)
      }
      return parent
    }
  },
  created() {
    this.loadCertTemplList()
  },
  methods: {
    loadCertTemplList() {
      getCertTemplList().then(res => {
        this.certTemplList = res
      })
    }
  }
}
</script>
<style scoped>

</style>
